-- =============================================
-- Budget Request Management System (MySQL)
-- ภาษาไทยทั้งระบบ + ข้อมูลงบประมาณตัวอย่าง + บัญชี admin
-- =============================================
DROP DATABASE IF EXISTS budget_management;
CREATE DATABASE budget_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE budget_management;

-- ตารางผู้ใช้
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role ENUM('admin','planner','procurement','committee') NOT NULL DEFAULT 'admin',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ตารางคำของบประมาณ
CREATE TABLE budget_requests (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    category VARCHAR(100),
    fiscal_year YEAR NOT NULL,
    requested_amount DECIMAL(15,2) DEFAULT 0,
    university_approval_amount DECIMAL(15,2) DEFAULT 0,
    cabinet_approval_amount DECIMAL(15,2) DEFAULT 0,
    status ENUM('draft','pending','approved','rejected') DEFAULT 'draft',
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- ตารางการพิจารณาคำของบประมาณ
CREATE TABLE budget_reviews (
    id INT PRIMARY KEY AUTO_INCREMENT,
    budget_request_id INT NOT NULL,
    reviewer_id INT NOT NULL,
    comments TEXT,
    approved_amount DECIMAL(15,2) DEFAULT 0,
    decision ENUM('approved','rejected') NOT NULL,
    reviewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (budget_request_id) REFERENCES budget_requests(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewer_id) REFERENCES users(id) ON DELETE CASCADE
);

-- ตารางการเบิกจ่ายจริง
CREATE TABLE disbursements (
    id INT PRIMARY KEY AUTO_INCREMENT,
    budget_request_id INT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    description TEXT,
    document_url VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (budget_request_id) REFERENCES budget_requests(id) ON DELETE CASCADE
);

-- ตารางฟอร์มคำของบประมาณ
CREATE TABLE budget_forms (
    id INT PRIMARY KEY AUTO_INCREMENT,
    form_name VARCHAR(255),
    fiscal_year YEAR,
    category VARCHAR(100),
    file_url VARCHAR(255),
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ตารางการส่งแบบฟอร์ม
CREATE TABLE form_submissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    budget_request_id INT NOT NULL,
    file_url VARCHAR(255),
    submitted_by INT,
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (budget_request_id) REFERENCES budget_requests(id) ON DELETE CASCADE,
    FOREIGN KEY (submitted_by) REFERENCES users(id) ON DELETE SET NULL
);

-- ----------------------
-- SEED ข้อมูลเริ่มต้น
-- ----------------------
-- admin / 123456 (รหัสผ่านถูกเข้ารหัสในฝั่งแอปตอนเริ่มต้น)
INSERT INTO users (username, password_hash, full_name, role) 
VALUES ('admin', '$2b$10$6Wk0NqN4G7kX3k9O0/1fFekoZcCzK9QY3cX1zlpQ3xVfVtB7X6f5S', 'ผู้ดูแลระบบ', 'admin');
-- หมายเหตุ: hash นี้คือ bcrypt ของรหัสผ่าน "123456"

-- คำของบประมาณตัวอย่าง
INSERT INTO budget_requests (title, category, fiscal_year, requested_amount, university_approval_amount, cabinet_approval_amount, status, created_by)
VALUES
('จัดซื้อคอมพิวเตอร์ห้องปฏิบัติการ', 'ครุภัณฑ์', 2025, 500000.00, 450000.00, 450000.00, 'approved', 1),
('ปรับปรุงระบบเครือข่าย', 'ซ่อมบำรุง', 2025, 250000.00, 200000.00, 0.00, 'pending', 1),
('โครงการฝึกอบรมบุคลากร', 'อบรม/สัมมนา', 2025, 150000.00, 0.00, 0.00, 'draft', 1);

-- เบิกจ่ายตัวอย่าง
INSERT INTO disbursements (budget_request_id, amount, description, document_url)
VALUES
(1, 120000.00, 'งวดที่ 1 จัดซื้ออุปกรณ์ชุดที่ 1', 'https://example.com/invoice1.pdf'),
(1, 80000.00, 'งวดที่ 2 จัดซื้ออุปกรณ์ชุดที่ 2', 'https://example.com/invoice2.pdf');

-- ฟอร์มตัวอย่าง
INSERT INTO budget_forms (form_name, fiscal_year, category, file_url)
VALUES
('แบบฟอร์มคำของบปี 2568 (ครุภัณฑ์)', 2025, 'ครุภัณฑ์', 'https://example.com/form-asset-2025.pdf'),
('แบบฟอร์มคำของบปี 2568 (ซ่อมบำรุง)', 2025, 'ซ่อมบำรุง', 'https://example.com/form-maint-2025.pdf');

-- การส่งแบบฟอร์มตัวอย่าง
INSERT INTO form_submissions (budget_request_id, file_url, submitted_by)
VALUES (1, 'https://example.com/submission1.pdf', 1);
