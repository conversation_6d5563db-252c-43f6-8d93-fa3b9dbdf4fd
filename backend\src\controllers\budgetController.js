// จัดการคำของบประมาณ
const db = require('../config/db');

exports.list = async (req, res) => {
  const { year, status } = req.query;
  let sql = `SELECT br.*, u.full_name AS created_by_name,
    (SELECT IFNULL(SUM(amount),0) FROM disbursements d WHERE d.budget_request_id = br.id) AS spent_amount
    FROM budget_requests br LEFT JOIN users u ON br.created_by = u.id WHERE 1=1`;
  const params = [];
  if (year) { sql += ' AND br.fiscal_year=?'; params.push(year); }
  if (status) { sql += ' AND br.status=?'; params.push(status); }
  sql += ' ORDER BY br.id DESC';
  const [rows] = await db.query(sql, params);
  res.json(rows);
};

exports.get = async (req, res) => {
  const { id } = req.params;
  const [[row]] = await db.query('SELECT * FROM budget_requests WHERE id=?', [id]);
  if (!row) return res.status(404).json({ message: 'ไม่พบรายการ' });
  res.json(row);
};

exports.create = async (req, res) => {
  const { title, category, fiscal_year, requested_amount } = req.body;
  await db.query(
    'INSERT INTO budget_requests (title, category, fiscal_year, requested_amount, status, created_by) VALUES (?,?,?,?,?,?)',
    [title, category, fiscal_year, requested_amount || 0, 'pending', req.user.id]
  );
  res.status(201).json({ message: 'สร้างคำของบสำเร็จ' });
};

exports.update = async (req, res) => {
  const { id } = req.params;
  const { title, category, fiscal_year, requested_amount, university_approval_amount, cabinet_approval_amount, status } = req.body;
  await db.query(
    `UPDATE budget_requests SET title=?, category=?, fiscal_year=?, requested_amount=?, 
     university_approval_amount=?, cabinet_approval_amount=?, status=? WHERE id=?`,
    [title, category, fiscal_year, requested_amount || 0, university_approval_amount || 0, cabinet_approval_amount || 0, status || 'pending', id]
  );
  res.json({ message: 'อัปเดตรายการสำเร็จ' });
};

exports.remove = async (req, res) => {
  const { id } = req.params;
  await db.query('DELETE FROM budget_requests WHERE id=?', [id]);
  res.json({ message: 'ลบรายการสำเร็จ' });
};
