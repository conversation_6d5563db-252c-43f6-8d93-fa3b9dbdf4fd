{"name": "budget-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@reduxjs/toolkit": "^2.8.2", "axios": "^1.6.7", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "react-redux": "^9.2.0", "react-router-dom": "^6.23.0"}, "devDependencies": {"@types/react": "^18.2.56", "@types/react-dom": "^18.2.19", "@vitejs/plugin-react": "^5.0.0", "autoprefixer": "^10.4.18", "postcss": "^8.4.38", "tailwindcss": "^3.4.3", "vite": "^5.2.0"}}