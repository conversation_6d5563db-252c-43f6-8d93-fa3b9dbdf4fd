# ระบบการจัดการงบประมาณ (Budget Request Management System)

ภาษาไทยทั้งระบบ — ครบ Backend + Frontend + Database พร้อมคอมเมนต์ในโค้ด

## บัญชีเริ่มต้น
- ผู้ดูแลระบบ: `admin / 123456`

> **หมายเหตุ:** มี seed ข้อมูลตัวอย่างใน `database.sql`

---

## การติดตั้ง (Local Machine)

### 1) ติดตั้งฐานข้อมูล (MySQL)
- สร้างฐานข้อมูลจากไฟล์ `database.sql`
  ```bash
  # บน XAMPP / HeidiSQL / MySQL CLI ก็ได้
  mysql -u root -p < database.sql
  ```

### 2) รัน Backend (Node.js + Express)
```bash
cd backend
cp .env.example .env  # แก้ค่าตามเครื่อง
npm install
npm run start
```

- API จะรันที่ `http://localhost:5000`

### 3) รัน Frontend (React + Vite + Tailwind + Redux)
```bash
cd ../frontend
npm install
npm run dev
```
- UI จะเปิดที่ `http://localhost:5173`

---

## สารบัญ API (ย่อ)
- `POST /api/auth/login` — เข้าสู่ระบบ (รับ `{username, password}`)
- `GET /api/budgets` — รายการคำของบ (ต้องใส่ Bearer Token)
- `POST /api/budgets` — เพิ่มคำของบ
- `PUT /api/budgets/:id` — แก้ไขคำของบ
- `DELETE /api/budgets/:id` — ลบคำของบ
- `GET /api/reviews` — รายการผลพิจารณา
- `POST /api/reviews` — บันทึกผลพิจารณา
- `GET /api/disbursements` — รายการเบิกจ่าย
- `POST /api/disbursements` — เพิ่มเบิกจ่าย
- `DELETE /api/disbursements/:id` — ลบเบิกจ่าย
- `GET /api/forms` — รายการแบบฟอร์ม
- `POST /api/forms` — เพิ่มแบบฟอร์ม
- `DELETE /api/forms/:id` — ลบแบบฟอร์ม
- `GET /api/users` — จัดการผู้ใช้ (เฉพาะ admin)
- ฯลฯ

ทุก API ใช้ JWT ผ่าน Header: `Authorization: Bearer <token>`

---

## โครงสร้างโฟลเดอร์

```
budget-management-system/
├── backend/
│   ├── server.js
│   ├── package.json
│   ├── .env.example
│   └── src/
│       ├── config/db.js
│       ├── middleware/auth.js
│       ├── controllers/
│       └── routes/
├── frontend/
│   ├── index.html
│   ├── package.json
│   └── src/...
└── database.sql
```

---

## หมายเหตุการใช้งาน
- ค่า baseURL ของ Frontend ตั้งไว้ที่ `http://localhost:5000/api` ใน `src/api.js`
- หากรัน backend บนพอร์ตอื่น ให้ปรับแก้ URL ตรงนั้น
- โค้ดใส่คอมเมนต์ไทยทุกส่วนหลักเพื่อให้อ่านเข้าใจง่าย
