// แบบฟอร์มคำของบประมาณ (ดาวน์โหลด)
const db = require('../config/db');

exports.list = async (req, res) => {
  const [rows] = await db.query('SELECT * FROM budget_forms ORDER BY id DESC');
  res.json(rows);
};

exports.create = async (req, res) => {
  const { form_name, fiscal_year, category, file_url } = req.body;
  await db.query('INSERT INTO budget_forms (form_name, fiscal_year, category, file_url) VALUES (?,?,?,?)',
    [form_name, fiscal_year, category, file_url]);
  res.status(201).json({ message: 'เพิ่มแบบฟอร์มแล้ว' });
};

exports.remove = async (req, res) => {
  const { id } = req.params;
  await db.query('DELETE FROM budget_forms WHERE id=?', [id]);
  res.json({ message: 'ลบแบบฟอร์มแล้ว' });
};
