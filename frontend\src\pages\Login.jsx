import { useState } from 'react'
import api from '../api'
import { useDispatch } from 'react-redux'
import { setAuth } from '../store/authSlice'
import toast from 'react-hot-toast'

export default function Login() {
  const [username, setUsername] = useState('admin')
  const [password, setPassword] = useState('123456')
  const dispatch = useDispatch()

  const submit = async (e) => {
    e.preventDefault()
    try {
      const { data } = await api.post('/auth/login', { username, password })
      dispatch(setAuth(data))
      toast.success('เข้าสู่ระบบสำเร็จ')
      location.href = '/'
    } catch (err) {
      toast.error(err.response?.data?.message || 'เข้าสู่ระบบไม่สำเร็จ')
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center">
      <form onSubmit={submit} className="bg-white p-8 rounded shadow w-96">
        <h1 className="text-2xl font-bold mb-4">เข้าสู่ระบบ</h1>
        <label className="block text-sm mb-1">ชื่อผู้ใช้</label>
        <input className="border w-full p-2 mb-3" value={username} onChange={e=>setUsername(e.target.value)} />
        <label className="block text-sm mb-1">รหัสผ่าน</label>
        <input type="password" className="border w-full p-2 mb-5" value={password} onChange={e=>setPassword(e.target.value)} />
        <button className="w-full bg-blue-600 text-white py-2 rounded">เข้าสู่ระบบ</button>
      </form>
    </div>
  )
}
