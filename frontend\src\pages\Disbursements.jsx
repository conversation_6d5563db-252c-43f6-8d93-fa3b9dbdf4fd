import { useEffect, useState } from 'react'
import api from '../api'
import toast from 'react-hot-toast'

export default function Disbursements() {
  const [budgets, setBudgets] = useState([])
  const [rows, setRows] = useState([])
  const [form, setForm] = useState({ budget_request_id:'', amount:0, description:'', document_url:'' })

  const refresh = async () => {
    const b = await api.get('/budgets')
    setBudgets(b.data)
    const d = await api.get('/disbursements')
    setRows(d.data)
  }

  useEffect(() => { refresh() }, [])

  const save = async () => {
    try {
      await api.post('/disbursements', form)
      toast.success('บันทึกเบิกจ่ายแล้ว')
      setForm({ budget_request_id:'', amount:0, description:'', document_url:'' })
      refresh()
    } catch {
      toast.error('ไม่สามารถบันทึกได้')
    }
  }

  const remove = async (id) => {
    if (!confirm('ลบรายการนี้?')) return
    await api.delete(`/disbursements/${id}`)
    toast.success('ลบแล้ว')
    refresh()
  }

  return (
    <div>
      <h2 className="text-xl font-bold mb-4">การเบิกจ่ายจริง</h2>
      <div className="bg-white border rounded p-4 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
          <select className="border p-2" value={form.budget_request_id} onChange={e=>setForm({...form, budget_request_id:e.target.value})}>
            <option value="">เลือกรายการคำของบ</option>
            {budgets.map(b=><option key={b.id} value={b.id}>{b.title}</option>)}
          </select>
          <input type="number" className="border p-2" placeholder="จำนวนเงิน" value={form.amount} onChange={e=>setForm({...form, amount:Number(e.target.value)})} />
          <input className="border p-2" placeholder="รายละเอียด" value={form.description} onChange={e=>setForm({...form, description:e.target.value})} />
          <input className="border p-2" placeholder="ลิงก์เอกสาร (เช่น ใบเสร็จ)" value={form.document_url} onChange={e=>setForm({...form, document_url:e.target.value})} />
        </div>
        <div className="mt-3">
          <button className="bg-blue-600 text-white px-4 py-2 rounded" onClick={save}>บันทึก</button>
        </div>
      </div>

      <div className="bg-white border rounded">
        <table className="w-full text-sm">
          <thead>
            <tr className="bg-gray-100 text-left">
              <th className="p-2">คำของบ</th>
              <th className="p-2">จำนวนเงิน</th>
              <th className="p-2">รายละเอียด</th>
              <th className="p-2">เอกสาร</th>
              <th className="p-2"></th>
            </tr>
          </thead>
          <tbody>
            {rows.map(r => (
              <tr key={r.id} className="border-t">
                <td className="p-2">{r.budget_request_id}</td>
                <td className="p-2">{Number(r.amount).toLocaleString()}</td>
                <td className="p-2">{r.description}</td>
                <td className="p-2"><a className="text-blue-600 underline" href={r.document_url} target="_blank">เปิด</a></td>
                <td className="p-2 text-right"><button className="text-red-600" onClick={()=>remove(r.id)}>ลบ</button></td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}
