// แอปหลัก + เส้นทางการนำทางภาษาไทย
import { Routes, Route, Navigate } from 'react-router-dom'
import Login from './pages/Login'
import Dashboard from './pages/Dashboard'
import Budgets from './pages/Budgets'
import Reviews from './pages/Reviews'
import Disbursements from './pages/Disbursements'
import Forms from './pages/Forms'
import Layout from './components/Layout'
import { useSelector } from 'react-redux'

function PrivateRoute({ children }) {
  const token = useSelector(s => s.auth.token)
  return token ? children : <Navigate to="/login" replace />
}

export default function App() {
  return (
    <Routes>
      <Route path="/login" element={<Login />} />
      <Route path="/" element={<PrivateRoute><Layout /></PrivateRoute>}>
        <Route index element={<Dashboard />} />
        <Route path="budgets" element={<Budgets />} />
        <Route path="reviews" element={<Reviews />} />
        <Route path="disbursements" element={<Disbursements />} />
        <Route path="forms" element={<Forms />} />
      </Route>
    </Routes>
  )
}
