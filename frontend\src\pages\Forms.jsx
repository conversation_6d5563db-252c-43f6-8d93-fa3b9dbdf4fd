import { useEffect, useState } from 'react'
import api from '../api'
import toast from 'react-hot-toast'

export default function Forms() {
  const [list, setList] = useState([])
  const [form, setForm] = useState({ form_name:'', fiscal_year:2025, category:'', file_url:'' })

  const load = async () => {
    const { data } = await api.get('/forms')
    setList(data)
  }

  useEffect(() => { load() }, [])

  const save = async () => {
    try {
      await api.post('/forms', form)
      toast.success('เพิ่มแบบฟอร์มแล้ว')
      setForm({ form_name:'', fiscal_year:2025, category:'', file_url:'' })
      load()
    } catch {
      toast.error('เพิ่มแบบฟอร์มไม่สำเร็จ')
    }
  }

  const remove = async (id) => {
    if (!confirm('ยืนยันการลบ?')) return
    await api.delete(`/forms/${id}`)
    toast.success('ลบแล้ว')
    load()
  }

  return (
    <div>
      <h2 className="text-xl font-bold mb-4">แบบฟอร์มคำของบประมาณ</h2>
      <div className="bg-white border rounded p-4 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
          <input className="border p-2" placeholder="ชื่อแบบฟอร์ม" value={form.form_name} onChange={e=>setForm({...form, form_name:e.target.value})} />
          <input type="number" className="border p-2" placeholder="ปีงบประมาณ" value={form.fiscal_year} onChange={e=>setForm({...form, fiscal_year:Number(e.target.value)})} />
          <input className="border p-2" placeholder="ประเภทงบ" value={form.category} onChange={e=>setForm({...form, category:e.target.value})} />
          <input className="border p-2" placeholder="ลิงก์ไฟล์" value={form.file_url} onChange={e=>setForm({...form, file_url:e.target.value})} />
        </div>
        <div className="mt-3">
          <button className="bg-blue-600 text-white px-4 py-2 rounded" onClick={save}>เพิ่ม</button>
        </div>
      </div>

      <div className="bg-white border rounded">
        <table className="w-full text-sm">
          <thead>
            <tr className="bg-gray-100 text-left">
              <th className="p-2">ชื่อแบบฟอร์ม</th>
              <th className="p-2">ปี</th>
              <th className="p-2">ประเภท</th>
              <th className="p-2">ลิงก์</th>
              <th className="p-2"></th>
            </tr>
          </thead>
          <tbody>
            {list.map(item => (
              <tr key={item.id} className="border-t">
                <td className="p-2">{item.form_name}</td>
                <td className="p-2">{item.fiscal_year}</td>
                <td className="p-2">{item.category}</td>
                <td className="p-2"><a className="text-blue-600 underline" href={item.file_url} target="_blank">ดาวน์โหลด</a></td>
                <td className="p-2 text-right"><button className="text-red-600" onClick={()=>remove(item.id)}>ลบ</button></td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}
