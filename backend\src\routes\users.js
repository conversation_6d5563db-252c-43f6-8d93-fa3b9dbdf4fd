const express = require('express');
const router = express.Router();
const { authenticate, authorize } = require('../middleware/auth');
const ctrl = require('../controllers/userController');

router.use(authenticate);
router.get('/', authorize(['admin']), ctrl.list);
router.post('/', authorize(['admin']), ctrl.create);
router.put('/:id', authorize(['admin']), ctrl.update);
router.delete('/:id', authorize(['admin']), ctrl.remove);

module.exports = router;
