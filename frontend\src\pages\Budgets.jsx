import { useEffect, useState } from 'react'
import api from '../api'
import toast from 'react-hot-toast'

export default function Budgets() {
  const [list, setList] = useState([])
  const [form, setForm] = useState({ title:'', category:'', fiscal_year: 2025, requested_amount: 0, status: 'pending' })
  const [editing, setEditing] = useState(null)

  const load = async () => {
    const { data } = await api.get('/budgets')
    setList(data)
  }

  useEffect(() => { load() }, [])

  const save = async () => {
    try {
      if (editing) {
        await api.put(`/budgets/${editing}`, form)
        toast.success('อัปเดตรายการสำเร็จ')
      } else {
        await api.post('/budgets', form)
        toast.success('สร้างคำของบสำเร็จ')
      }
      setForm({ title:'', category:'', fiscal_year: 2025, requested_amount: 0, status: 'pending' })
      setEditing(null)
      load()
    } catch (e) {
      toast.error('บันทึกไม่สำเร็จ')
    }
  }

  const edit = (item) => {
    setEditing(item.id)
    setForm({
      title: item.title, category: item.category, fiscal_year: item.fiscal_year,
      requested_amount: item.requested_amount, university_approval_amount: item.university_approval_amount,
      cabinet_approval_amount: item.cabinet_approval_amount, status: item.status
    })
  }

  const remove = async (id) => {
    if (!confirm('ยืนยันการลบรายการนี้?')) return
    await api.delete(`/budgets/${id}`)
    toast.success('ลบแล้ว')
    load()
  }

  return (
    <div>
      <h2 className="text-xl font-bold mb-4">คำของบประมาณ</h2>
      <div className="bg-white border rounded p-4 mb-6">
        <h3 className="font-semibold mb-3">{editing ? 'แก้ไขคำของบ' : 'เพิ่มคำของบใหม่'}</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          <input className="border p-2" placeholder="ชื่อรายการ" value={form.title} onChange={e=>setForm({...form, title:e.target.value})} />
          <input className="border p-2" placeholder="ประเภทงบ" value={form.category} onChange={e=>setForm({...form, category:e.target.value})} />
          <input className="border p-2" type="number" placeholder="ปีงบประมาณ" value={form.fiscal_year} onChange={e=>setForm({...form, fiscal_year:Number(e.target.value)})} />
          <input className="border p-2" type="number" placeholder="วงเงินที่ขอ" value={form.requested_amount} onChange={e=>setForm({...form, requested_amount:Number(e.target.value)})} />
          <input className="border p-2" type="number" placeholder="วงเงินมหาลัยอนุมัติ" value={form.university_approval_amount||''} onChange={e=>setForm({...form, university_approval_amount:Number(e.target.value)})} />
          <input className="border p-2" type="number" placeholder="วงเงิน ครม. อนุมัติ" value={form.cabinet_approval_amount||''} onChange={e=>setForm({...form, cabinet_approval_amount:Number(e.target.value)})} />
          <select className="border p-2" value={form.status} onChange={e=>setForm({...form, status:e.target.value})}>
            <option value="draft">ฉบับร่าง</option>
            <option value="pending">รอดำเนินการ</option>
            <option value="approved">อนุมัติแล้ว</option>
            <option value="rejected">ไม่อนุมัติ</option>
          </select>
        </div>
        <div className="mt-3">
          <button className="bg-blue-600 text-white px-4 py-2 rounded" onClick={save}>{editing ? 'บันทึกการแก้ไข' : 'เพิ่มรายการ'}</button>
          {editing && <button className="ml-2 px-4 py-2 rounded border" onClick={()=>{setEditing(null); setForm({ title:'', category:'', fiscal_year: 2025, requested_amount: 0, status:'pending' })}}>ยกเลิก</button>}
        </div>
      </div>

      <div className="bg-white border rounded">
        <table className="w-full text-sm">
          <thead>
            <tr className="bg-gray-100 text-left">
              <th className="p-2">ชื่อรายการ</th>
              <th className="p-2">หมวด</th>
              <th className="p-2">ปี</th>
              <th className="p-2">ขอ</th>
              <th className="p-2">ม.อนุมัติ</th>
              <th className="p-2">ครม.อนุมัติ</th>
              <th className="p-2">สถานะ</th>
              <th className="p-2"></th>
            </tr>
          </thead>
          <tbody>
            {list.map(item => (
              <tr key={item.id} className="border-t">
                <td className="p-2">{item.title}</td>
                <td className="p-2">{item.category}</td>
                <td className="p-2">{item.fiscal_year}</td>
                <td className="p-2">{Number(item.requested_amount).toLocaleString()}</td>
                <td className="p-2">{Number(item.university_approval_amount).toLocaleString()}</td>
                <td className="p-2">{Number(item.cabinet_approval_amount).toLocaleString()}</td>
                <td className="p-2">{item.status}</td>
                <td className="p-2 text-right">
                  <button className="text-blue-600 mr-2" onClick={()=>edit(item)}>แก้ไข</button>
                  <button className="text-red-600" onClick={()=>remove(item.id)}>ลบ</button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}
