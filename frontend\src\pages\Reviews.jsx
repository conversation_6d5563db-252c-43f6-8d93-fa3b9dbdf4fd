import { useEffect, useState } from 'react'
import api from '../api'
import toast from 'react-hot-toast'

export default function Reviews() {
  const [reviews, setReviews] = useState([])
  const [budgets, setBudgets] = useState([])
  const [form, setForm] = useState({ budget_request_id: '', approved_amount: 0, decision: 'approved', comments: '' })

  const load = async () => {
    const b = await api.get('/budgets')
    setBudgets(b.data)
    const r = await api.get('/reviews')
    setReviews(r.data)
  }

  useEffect(() => { load() }, [])

  const save = async () => {
    try {
      await api.post('/reviews', form)
      toast.success('บันทึกผลพิจารณาสำเร็จ')
      setForm({ budget_request_id: '', approved_amount: 0, decision: 'approved', comments: '' })
      load()
    } catch {
      toast.error('บันทึกไม่สำเร็จ')
    }
  }

  return (
    <div>
      <h2 className="text-xl font-bold mb-4">การพิจารณาคำของบประมาณ</h2>
      <div className="bg-white border rounded p-4 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          <select className="border p-2" value={form.budget_request_id} onChange={e=>setForm({...form, budget_request_id:e.target.value})}>
            <option value="">เลือกรายการคำของบ</option>
            {budgets.map(b=><option key={b.id} value={b.id}>{b.title} (ปี {b.fiscal_year})</option>)}
          </select>
          <input type="number" className="border p-2" placeholder="วงเงินที่อนุมัติจริง" value={form.approved_amount} onChange={e=>setForm({...form, approved_amount:Number(e.target.value)})} />
          <select className="border p-2" value={form.decision} onChange={e=>setForm({...form, decision:e.target.value})}>
            <option value="approved">อนุมัติ</option>
            <option value="rejected">ไม่อนุมัติ</option>
          </select>
          <input className="border p-2 md:col-span-3" placeholder="ความคิดเห็น" value={form.comments} onChange={e=>setForm({...form, comments:e.target.value})} />
        </div>
        <div className="mt-3">
          <button className="bg-blue-600 text-white px-4 py-2 rounded" onClick={save}>บันทึกผล</button>
        </div>
      </div>

      <div className="bg-white border rounded">
        <table className="w-full text-sm">
          <thead>
            <tr className="bg-gray-100 text-left">
              <th className="p-2">รายการ</th>
              <th className="p-2">ผลการพิจารณา</th>
              <th className="p-2">วงเงินอนุมัติ</th>
              <th className="p-2">ผู้พิจารณา</th>
              <th className="p-2">ความคิดเห็น</th>
            </tr>
          </thead>
          <tbody>
            {reviews.map(r => (
              <tr key={r.id} className="border-t">
                <td className="p-2">{r.budget_title}</td>
                <td className="p-2">{r.decision}</td>
                <td className="p-2">{Number(r.approved_amount).toLocaleString()}</td>
                <td className="p-2">{r.reviewer_name}</td>
                <td className="p-2">{r.comments}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}
