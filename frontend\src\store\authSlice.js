// จัดการสถานะการล็อกอินและข้อมูลผู้ใช้
import { createSlice } from '@reduxjs/toolkit'

const tokenFromStorage = localStorage.getItem('token') || null
const userFromStorage = localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user')) : null

const slice = createSlice({
  name: 'auth',
  initialState: { token: tokenFromStorage, user: userFromStorage },
  reducers: {
    setAuth: (state, action) => {
      state.token = action.payload.token
      state.user = action.payload.user
      localStorage.setItem('token', state.token)
      localStorage.setItem('user', JSON.stringify(state.user))
    },
    logout: (state) => {
      state.token = null
      state.user = null
      localStorage.removeItem('token')
      localStorage.removeItem('user')
    }
  }
})

export const { setAuth, logout } = slice.actions
export default slice.reducer
