import { NavLink, Outlet, useNavigate } from 'react-router-dom'
import { useDispatch, useSelector } from 'react-redux'
import { logout } from '../store/authSlice'

export default function Layout() {
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const user = useSelector(s => s.auth.user)

  const onLogout = () => {
    dispatch(logout())
    navigate('/login')
  }

  return (
    <div className="min-h-screen flex">
      <aside className="w-64 bg-white border-r">
        <div className="p-4 border-b">
          <h1 className="text-lg font-bold">ระบบการจัดการงบประมาณ</h1>
          <p className="text-sm text-gray-500">ยินดีต้อนรับ: {user?.full_name}</p>
        </div>
        <nav className="p-2">
          <NavLink to="/" end className="block px-3 py-2 rounded hover:bg-gray-100">แดชบอร์ด</NavLink>
          <NavLink to="/budgets" className="block px-3 py-2 rounded hover:bg-gray-100">คำของบประมาณ</NavLink>
          <NavLink to="/reviews" className="block px-3 py-2 rounded hover:bg-gray-100">การพิจารณา</NavLink>
          <NavLink to="/disbursements" className="block px-3 py-2 rounded hover:bg-gray-100">เบิกจ่ายจริง</NavLink>
          <NavLink to="/forms" className="block px-3 py-2 rounded hover:bg-gray-100">แบบฟอร์ม</NavLink>
        </nav>
        <div className="p-3">
          <button onClick={onLogout} className="w-full bg-red-500 text-white py-2 rounded">ออกจากระบบ</button>
        </div>
      </aside>
      <main className="flex-1 p-6">
        <Outlet />
      </main>
    </div>
  )
}
