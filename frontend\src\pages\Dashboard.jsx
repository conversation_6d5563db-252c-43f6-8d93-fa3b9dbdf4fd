import { useEffect, useState } from 'react'
import api from '../api'

export default function Dashboard() {
  const [stats, setStats] = useState({ total: 0, approved: 0, pending: 0, draft: 0, rejected: 0, totalRequested: 0, totalApproved: 0, totalSpent: 0 })

  useEffect(() => {
    ;(async () => {
      const { data } = await api.get('/budgets')
      const s = {
        total: data.length,
        approved: data.filter(x=>x.status==='approved').length,
        pending: data.filter(x=>x.status==='pending').length,
        draft: data.filter(x=>x.status==='draft').length,
        rejected: data.filter(x=>x.status==='rejected').length,
        totalRequested: data.reduce((a,b)=>a+(Number(b.requested_amount)||0), 0),
        totalApproved: data.reduce((a,b)=>a+(Number(b.university_approval_amount)||0)+(Number(b.cabinet_approval_amount)||0), 0),
        totalSpent: data.reduce((a,b)=>a+(Number(b.spent_amount)||0), 0)
      }
      setStats(s)
    })()
  }, [])

  return (
    <div>
      <h2 className="text-xl font-bold mb-4">ภาพรวมงบประมาณ</h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card title="จำนวนคำของบทั้งหมด" value={stats.total} />
        <Card title="อนุมัติแล้ว" value={stats.approved} />
        <Card title="รอดำเนินการ" value={stats.pending} />
        <Card title="ฉบับร่าง" value={stats.draft} />
        <Card title="ไม่อนุมัติ" value={stats.rejected} />
        <Card title="วงเงินที่ขอ (รวม)" value={stats.totalRequested.toLocaleString()} />
        <Card title="วงเงินที่อนุมัติ (ม./ครม.)" value={stats.totalApproved.toLocaleString()} />
        <Card title="วงเงินที่เบิกจ่ายจริง" value={stats.totalSpent.toLocaleString()} />
      </div>
    </div>
  )
}

function Card({ title, value }) {
  return (
    <div className="bg-white border rounded p-4">
      <div className="text-sm text-gray-500">{title}</div>
      <div className="text-2xl font-bold">{value}</div>
    </div>
  )
}
